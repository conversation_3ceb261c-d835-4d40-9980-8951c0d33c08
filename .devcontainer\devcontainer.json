
{
  "name": "Discourse",
  "image": "docker.io/discourse/discourse_dev:20250307-0016",
  "workspaceMount": "source=${localWorkspaceFolder},target=/workspace/discourse,type=bind",
  "workspaceFolder": "/workspace/discourse",
  "postStartCommand": "./.devcontainer/scripts/start.rb",
  "forwardPorts": [
    9292, // bin/unicorn
    3000, // bin/rails s
    4200, // ember-cli
    8025, // mailhog
    9229  // chrome remote debug
  ],
  "remoteUser": "discourse",
  "remoteEnv": {
    "RAILS_DEVELOPMENT_HOSTS": ".app.github.dev",
    "PGUSER": "discourse",
    "SELENIUM_FORWARD_DEVTOOLS_TO_PORT": "9229",
    // 数据库配置
    "DISCOURSE_DATABASE_HOST": "***********",
    "DISCOURSE_DATABASE_PORT": "31597", 
    "DISCOURSE_DATABASE_USERNAME": "bn_discourse",
    "DISCOURSE_DATABASE_PASSWORD": "O*uelb&X8aMpt%$EcbU3yCdXm7TWd^Jr",
    "DISCOURSE_DATABASE_NAME": "bitnami_application",
    // Redis 配置
    "DISCOURSE_REDIS_HOST": "***********",
    "DISCOURSE_REDIS_PORT": "31550",
    "DISCOURSE_REDIS_PASSWORD": "KUcttXBPBmLHaMT6%HOlSCcH8A7$%%Dx",
    "DISCOURSE_REDIS_DB": "0",
    //SMTP
    "DISCOURSE_SMTP_HOST": "smtp.qiye.aliyun.com",
    "DISCOURSE_SMTP_PORT_NUMBER": "25",
    "DISCOURSE_SMTP_USER": "<EMAIL>",
    "DISCOURSE_SMTP_PASSWORD": "mvX0Rgjv9qS8pyAR"
  },
  "mounts": [
    "source=${localWorkspaceFolderBasename}-node_modules,target=${containerWorkspaceFolder}/node_modules,type=volume",
    "source=${localWorkspaceFolderBasename}-pg,target=/shared/postgres_data,type=volume",
    "source=${localWorkspaceFolderBasename}-redis,target=/shared/redis,type=volume"
  ],
  "customizations": {
    "vscode": {
      "extensions": [
        "Shopify.ruby-lsp",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "lifeart.vscode-glimmer-syntax",
        "typed-ember.glint-vscode",
        "stylelint.vscode-stylelint"
      ]
    }
  }
}
